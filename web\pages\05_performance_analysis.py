#!/usr/bin/env python3
"""
策略性能分析页面
Strategy Performance Analysis Page

提供高级性能指标分析、策略对比和智能推荐功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from web.utils.performance_analyzer import performance_analyzer
    from web.utils.async_backtest_manager import async_backtest_manager
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="Performance Analysis - TBTrade",
    page_icon="📊",
    layout="wide"
)

def create_metrics_comparison_chart(strategies_data):
    """创建指标对比图表"""
    if not strategies_data:
        return None
    
    metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
    metric_names = ['总收益率', '夏普比率', '最大回撤', '胜率']
    
    fig = go.Figure()
    
    for i, strategy in enumerate(strategies_data):
        values = [strategy.get(metric, 0) for metric in metrics]
        # 回撤取负值用于显示
        values[2] = -values[2] if values[2] > 0 else 0
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=metric_names,
            fill='toself',
            name=strategy.get('name', f'策略{i+1}'),
            line=dict(width=2)
        ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[-0.5, 1]
            )
        ),
        showlegend=True,
        title="策略性能雷达图",
        height=500
    )
    
    return fig

def create_performance_heatmap(strategies_data):
    """创建性能热力图"""
    if not strategies_data:
        return None
    
    metrics = ['total_return', 'annual_return', 'sharpe_ratio', 'calmar_ratio', 
               'win_rate', 'profit_factor', 'max_drawdown']
    metric_names = ['总收益率', '年化收益率', '夏普比率', '卡尔玛比率', 
                   '胜率', '盈利因子', '最大回撤']
    
    # 构建数据矩阵
    data_matrix = []
    strategy_names = []
    
    for strategy in strategies_data:
        strategy_names.append(strategy.get('name', '未命名策略'))
        row = []
        for metric in metrics:
            value = strategy.get(metric, 0)
            # 标准化处理
            if metric == 'max_drawdown':
                value = -value  # 回撤转为负值
            row.append(value)
        data_matrix.append(row)
    
    if not data_matrix:
        return None
    
    # 创建热力图
    fig = go.Figure(data=go.Heatmap(
        z=data_matrix,
        x=metric_names,
        y=strategy_names,
        colorscale='RdYlGn',
        text=[[f'{val:.3f}' for val in row] for row in data_matrix],
        texttemplate="%{text}",
        textfont={"size": 10},
        hoverongaps=False
    ))
    
    fig.update_layout(
        title="策略性能热力图",
        xaxis_title="性能指标",
        yaxis_title="策略",
        height=400
    )
    
    return fig

def display_advanced_metrics(metrics):
    """显示高级性能指标"""
    st.subheader("📊 高级性能指标")
    
    # 第一行：收益相关指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "总收益率",
            f"{metrics.get('total_return', 0):.2%}",
            help="整个回测期间的总收益率"
        )
    
    with col2:
        st.metric(
            "年化收益率",
            f"{metrics.get('annual_return', 0):.2%}",
            help="年化收益率"
        )
    
    with col3:
        st.metric(
            "夏普比率",
            f"{metrics.get('sharpe_ratio', 0):.2f}",
            help="风险调整后的收益指标"
        )
    
    with col4:
        st.metric(
            "索提诺比率",
            f"{metrics.get('sortino_ratio', 0):.2f}",
            help="下行风险调整后的收益指标"
        )
    
    # 第二行：风险相关指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "最大回撤",
            f"{metrics.get('max_drawdown', 0):.2%}",
            help="最大回撤幅度"
        )
    
    with col2:
        st.metric(
            "卡尔玛比率",
            f"{metrics.get('calmar_ratio', 0):.2f}",
            help="年化收益率与最大回撤的比值"
        )
    
    with col3:
        st.metric(
            "波动率",
            f"{metrics.get('volatility', 0):.2%}",
            help="年化波动率"
        )
    
    with col4:
        st.metric(
            "盈利因子",
            f"{metrics.get('profit_factor', 0):.2f}",
            help="总盈利与总亏损的比值"
        )
    
    # 第三行：交易相关指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "胜率",
            f"{metrics.get('win_rate', 0):.1%}",
            help="盈利交易占总交易的比例"
        )
    
    with col2:
        st.metric(
            "盈亏比",
            f"{metrics.get('profit_loss_ratio', 0):.2f}",
            help="平均盈利与平均亏损的比值"
        )
    
    with col3:
        st.metric(
            "最大连胜",
            f"{metrics.get('max_consecutive_wins', 0)}次",
            help="最大连续盈利次数"
        )
    
    with col4:
        st.metric(
            "交易频率",
            f"{metrics.get('trades_per_month', 0):.1f}次/月",
            help="平均每月交易次数"
        )

def main():
    """主函数"""
    
    # 页面标题
    st.title("📊 策略性能分析")
    st.markdown("---")
    
    # 创建标签页
    tab1, tab2, tab3 = st.tabs(["📈 单策略分析", "⚖️ 策略对比", "🎯 智能推荐"])
    
    with tab1:
        st.header("单策略深度分析")
        
        # 获取已完成的回测任务
        completed_tasks = [
            task for task in async_backtest_manager.get_all_tasks()
            if task['status'] == 'completed' and task.get('result')
        ]
        
        if not completed_tasks:
            st.info("暂无已完成的回测任务，请先在回测分析页面执行回测。")
            return
        
        # 选择要分析的任务
        task_options = {
            f"任务 {task['task_id'][:8]} - {', '.join(task['config'].get('symbols', []))}": task
            for task in completed_tasks
        }
        
        selected_task_name = st.selectbox(
            "选择要分析的回测任务",
            list(task_options.keys()),
            help="选择一个已完成的回测任务进行深度分析"
        )
        
        if selected_task_name:
            selected_task = task_options[selected_task_name]
            result = selected_task['result']
            
            # 计算高级指标
            equity_curve = pd.DataFrame(result.get('equity_curve', []))
            trades = pd.DataFrame(result.get('trades', []))
            initial_capital = selected_task['config'].get('initial_capital', 10000)
            
            metrics = performance_analyzer.calculate_advanced_metrics(
                equity_curve, trades, initial_capital
            )
            
            # 显示高级指标
            display_advanced_metrics(metrics)
            
            # 详细分析
            st.markdown("---")
            st.subheader("📋 详细分析报告")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**策略配置**")
                config = selected_task['config']
                st.json(config.get('strategy_params', {}))
                
                st.markdown("**风险评估**")
                risk_level = "低风险" if metrics.get('max_drawdown', 0) < 0.1 else \
                           "中等风险" if metrics.get('max_drawdown', 0) < 0.2 else "高风险"
                st.write(f"风险等级: {risk_level}")
                
                sharpe_quality = "优秀" if metrics.get('sharpe_ratio', 0) > 2 else \
                               "良好" if metrics.get('sharpe_ratio', 0) > 1 else \
                               "一般" if metrics.get('sharpe_ratio', 0) > 0 else "较差"
                st.write(f"夏普比率评级: {sharpe_quality}")
            
            with col2:
                st.markdown("**交易统计**")
                st.write(f"总交易次数: {metrics.get('total_trades', 0)}")
                st.write(f"盈利交易: {int(metrics.get('total_trades', 0) * metrics.get('win_rate', 0))}")
                st.write(f"亏损交易: {metrics.get('total_trades', 0) - int(metrics.get('total_trades', 0) * metrics.get('win_rate', 0))}")
                st.write(f"期望收益: {metrics.get('expected_return', 0):.2f}")
                
                st.markdown("**建议**")
                if metrics.get('win_rate', 0) < 0.4:
                    st.warning("胜率较低，建议优化入场条件")
                if metrics.get('max_drawdown', 0) > 0.2:
                    st.warning("回撤较大，建议加强风险控制")
                if metrics.get('sharpe_ratio', 0) > 1.5:
                    st.success("夏普比率优秀，策略表现良好")
    
    with tab2:
        st.header("策略对比分析")
        
        # 多选回测任务进行对比
        if not completed_tasks:
            st.info("暂无已完成的回测任务，请先在回测分析页面执行回测。")
            return
        
        task_options = {
            f"任务 {task['task_id'][:8]} - {', '.join(task['config'].get('symbols', []))}": task
            for task in completed_tasks
        }
        
        selected_tasks = st.multiselect(
            "选择要对比的回测任务（最多5个）",
            list(task_options.keys()),
            help="选择多个回测任务进行对比分析"
        )
        
        if len(selected_tasks) >= 2:
            # 准备对比数据
            strategy_results = []
            
            for i, task_name in enumerate(selected_tasks[:5]):  # 最多5个
                task = task_options[task_name]
                result = task['result']
                
                strategy_result = {
                    'name': f"策略{i+1}",
                    'equity_curve': pd.DataFrame(result.get('equity_curve', [])),
                    'trades': pd.DataFrame(result.get('trades', [])),
                    'initial_capital': task['config'].get('initial_capital', 10000),
                    'config': task['config']
                }
                strategy_results.append(strategy_result)
            
            # 执行对比分析
            comparison = performance_analyzer.compare_strategies(strategy_results)
            
            if 'error' not in comparison:
                # 显示对比结果
                st.subheader("📊 对比概览")
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("对比策略数", comparison['summary'].get('total_strategies', 0))
                
                with col2:
                    st.metric("最佳收益率", f"{comparison['summary'].get('best_return', 0):.2%}")
                
                with col3:
                    st.metric("最佳夏普比率", f"{comparison['summary'].get('best_sharpe', 0):.2f}")
                
                # 性能雷达图
                st.subheader("🎯 性能雷达图")
                radar_chart = create_metrics_comparison_chart(comparison['strategies'])
                if radar_chart:
                    st.plotly_chart(radar_chart, use_container_width=True)
                
                # 性能热力图
                st.subheader("🔥 性能热力图")
                heatmap = create_performance_heatmap(comparison['strategies'])
                if heatmap:
                    st.plotly_chart(heatmap, use_container_width=True)
                
                # 排名表
                st.subheader("🏆 策略排名")
                
                ranking_tabs = st.tabs(["总收益率", "夏普比率", "胜率", "盈利因子"])
                
                ranking_metrics = ['total_return', 'sharpe_ratio', 'win_rate', 'profit_factor']
                
                for i, (tab, metric) in enumerate(zip(ranking_tabs, ranking_metrics)):
                    with tab:
                        if metric in comparison['rankings']:
                            ranking_data = []
                            for rank, strategy_name in enumerate(comparison['rankings'][metric], 1):
                                strategy = next(s for s in comparison['strategies'] if s['name'] == strategy_name)
                                ranking_data.append({
                                    '排名': rank,
                                    '策略': strategy_name,
                                    '数值': f"{strategy.get(metric, 0):.3f}"
                                })
                            
                            st.dataframe(pd.DataFrame(ranking_data), use_container_width=True, hide_index=True)
                
                # 最佳策略推荐
                if comparison['best_strategy']:
                    st.subheader("🥇 综合最佳策略")
                    best = comparison['best_strategy']
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.success(f"**推荐策略**: {best.get('name', '未知')}")
                        st.write(f"**综合评分**: {best.get('composite_score', 0):.2f}")
                        st.write(f"**总收益率**: {best.get('total_return', 0):.2%}")
                        st.write(f"**夏普比率**: {best.get('sharpe_ratio', 0):.2f}")
                    
                    with col2:
                        st.write(f"**最大回撤**: {best.get('max_drawdown', 0):.2%}")
                        st.write(f"**胜率**: {best.get('win_rate', 0):.1%}")
                        st.write(f"**盈利因子**: {best.get('profit_factor', 0):.2f}")
            
            else:
                st.error(comparison['error'])
        
        elif len(selected_tasks) == 1:
            st.info("请至少选择2个策略进行对比分析")
    
    with tab3:
        st.header("智能策略推荐")
        
        # 用户风险偏好配置
        st.subheader("🎯 风险偏好设置")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            risk_tolerance = st.selectbox(
                "风险承受能力",
                ["low", "medium", "high"],
                format_func=lambda x: {"low": "保守型", "medium": "平衡型", "high": "激进型"}[x],
                index=1,
                help="选择您的风险承受能力"
            )
        
        with col2:
            return_target = st.slider(
                "目标年化收益率",
                min_value=0.05,
                max_value=1.0,
                value=0.15,
                step=0.05,
                format="%.0%",
                help="设置您期望的年化收益率目标"
            )
        
        with col3:
            max_drawdown_tolerance = st.slider(
                "最大可接受回撤",
                min_value=0.05,
                max_value=0.50,
                value=0.20,
                step=0.05,
                format="%.0%",
                help="设置您能接受的最大回撤幅度"
            )
        
        if st.button("🔍 生成推荐", type="primary"):
            if not completed_tasks:
                st.warning("暂无已完成的回测任务，无法生成推荐")
                return
            
            # 准备用户配置
            user_profile = {
                'risk_tolerance': risk_tolerance,
                'return_target': return_target,
                'max_drawdown_tolerance': max_drawdown_tolerance
            }
            
            # 准备策略数据
            available_strategies = []
            
            for i, task in enumerate(completed_tasks):
                result = task['result']
                equity_curve = pd.DataFrame(result.get('equity_curve', []))
                trades = pd.DataFrame(result.get('trades', []))
                initial_capital = task['config'].get('initial_capital', 10000)
                
                metrics = performance_analyzer.calculate_advanced_metrics(
                    equity_curve, trades, initial_capital
                )
                metrics['name'] = f"策略{i+1} ({task['task_id'][:8]})"
                metrics['config'] = task['config']
                
                available_strategies.append(metrics)
            
            # 生成推荐
            recommendation = performance_analyzer.generate_strategy_recommendation(
                user_profile, available_strategies
            )
            
            if 'error' not in recommendation:
                st.subheader("🎯 推荐结果")
                
                # 推荐摘要
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("可用策略", recommendation['total_available'])
                
                with col2:
                    st.metric("符合条件", recommendation['suitable_count'])
                
                with col3:
                    st.metric("推荐策略", len(recommendation['recommendations']))
                
                # 推荐说明
                if recommendation.get('explanation'):
                    st.info(recommendation['explanation'])
                
                # 推荐列表
                if recommendation['recommendations']:
                    st.subheader("📋 推荐策略列表")
                    
                    for i, strategy in enumerate(recommendation['recommendations'], 1):
                        with st.expander(f"推荐 {i}: {strategy.get('name', '未知策略')} (评分: {strategy.get('recommendation_score', 0):.1f})"):
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                st.write(f"**总收益率**: {strategy.get('total_return', 0):.2%}")
                                st.write(f"**年化收益率**: {strategy.get('annual_return', 0):.2%}")
                                st.write(f"**最大回撤**: {strategy.get('max_drawdown', 0):.2%}")
                                st.write(f"**夏普比率**: {strategy.get('sharpe_ratio', 0):.2f}")
                            
                            with col2:
                                st.write(f"**胜率**: {strategy.get('win_rate', 0):.1%}")
                                st.write(f"**盈利因子**: {strategy.get('profit_factor', 0):.2f}")
                                st.write(f"**交易频率**: {strategy.get('trades_per_month', 0):.1f}次/月")
                                st.write(f"**推荐评分**: {strategy.get('recommendation_score', 0):.1f}")
                
                else:
                    st.warning("根据您的风险偏好，暂时没有找到合适的策略推荐。建议调整风险偏好设置或执行更多回测。")
            
            else:
                st.error(recommendation['error'])

if __name__ == "__main__":
    main()
