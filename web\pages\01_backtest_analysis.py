"""
回测分析页面
提供交互式回测配置和结果可视化功能
"""

import streamlit as st
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入必要的组件和工具
from components.charts import (
    create_equity_curve_chart, create_returns_distribution_chart,
    create_drawdown_chart, create_monthly_returns_heatmap, create_risk_metrics_chart
)
from components.tables import display_trades_table, display_performance_summary
from config import BACKTEST_CONFIG, CHART_CONFIG
from utils.tbtrade_integration import tbtrade_integration
from utils.data_loader import get_available_symbols, load_market_data
from utils.async_backtest_manager import async_backtest_manager, TaskStatus
import json

# 页面配置
st.set_page_config(
    page_title="Backtest Analysis - TBTrade",
    page_icon="📊",
    layout="wide"
)

# 预设参数配置
PRESET_PARAMS = {
    "保守型": {
        "ema_short": 21,
        "ema_medium": 55,
        "ema_long": 200,
        "stop_loss_pct": 0.10,
        "take_profit_pct": 0.30,
        "signal_cooldown_days": 45
    },
    "平衡型": {
        "ema_short": 21,
        "ema_medium": 55,
        "ema_long": 200,
        "stop_loss_pct": 0.15,
        "take_profit_pct": 0.50,
        "signal_cooldown_days": 30
    },
    "激进型": {
        "ema_short": 12,
        "ema_medium": 26,
        "ema_long": 100,
        "stop_loss_pct": 0.20,
        "take_profit_pct": 0.80,
        "signal_cooldown_days": 15
    },
    "短线型": {
        "ema_short": 9,
        "ema_medium": 21,
        "ema_long": 55,
        "stop_loss_pct": 0.08,
        "take_profit_pct": 0.25,
        "signal_cooldown_days": 7
    },
    "长线型": {
        "ema_short": 50,
        "ema_medium": 100,
        "ema_long": 200,
        "stop_loss_pct": 0.25,
        "take_profit_pct": 1.00,
        "signal_cooldown_days": 60
    }
}

def load_preset_params(preset_name):
    """加载预设参数到session state"""
    if preset_name in PRESET_PARAMS:
        params = PRESET_PARAMS[preset_name]
        for key, value in params.items():
            st.session_state[f"preset_{key}"] = value

def save_preset_params(preset_name):
    """保存当前参数为预设"""
    # 这里可以扩展为保存到文件或数据库
    # 目前只是演示功能
    pass

def get_preset_value(key, default_value):
    """获取预设值或默认值"""
    return st.session_state.get(f"preset_{key}", default_value)

def convert_async_results_to_display_format(async_result):
    """将异步回测结果转换为显示格式"""
    try:
        summary = async_result.get('summary', {})
        trades = async_result.get('trades', [])

        # 转换交易数据为DataFrame
        if trades:
            trades_df = pd.DataFrame(trades)
            # 确保包含必要的字段
            if 'return_pct' not in trades_df.columns and 'pnl' in trades_df.columns:
                # 计算收益率
                trades_df['return_pct'] = trades_df['pnl'] / abs(trades_df.get('entry_price', 1))
        else:
            trades_df = pd.DataFrame()

        # 生成资产曲线数据
        equity_curve = async_result.get('equity_curve', [])
        if equity_curve:
            equity_df = pd.DataFrame(equity_curve)
        else:
            # 如果没有资产曲线数据，生成简单的模拟数据
            initial_capital = async_result.get('config', {}).get('initial_capital', 10000)
            final_capital = summary.get('final_capital', initial_capital)
            equity_df = pd.DataFrame({
                'date': [pd.Timestamp.now() - pd.Timedelta(days=30), pd.Timestamp.now()],
                'equity': [initial_capital, final_capital]
            })

        # 构建性能指标
        performance_metrics = {
            "总收益率": summary.get('total_return', 0),
            "年化收益率": summary.get('total_return', 0) * 12,  # 简化计算
            "胜率": summary.get('win_rate', 0) / 100,
            "总交易次数": summary.get('total_trades', 0),
            "盈利交易": summary.get('profitable_trades', 0),
            "亏损交易": summary.get('total_trades', 0) - summary.get('profitable_trades', 0),
            "最大回撤": summary.get('max_drawdown', 0),
            "夏普比率": summary.get('sharpe_ratio', 0),
            "总盈亏": summary.get('final_capital', 10000) - async_result.get('config', {}).get('initial_capital', 10000),
            "最大单笔盈利": max([t.get('pnl', 0) for t in trades], default=0),
            "最大单笔亏损": min([t.get('pnl', 0) for t in trades], default=0)
        }

        return {
            'equity_curve': equity_df,
            'trades': trades_df,
            'performance': performance_metrics,
            'symbols': async_result.get('config', {}).get('symbols', []),
            'date_range': [
                async_result.get('config', {}).get('start_date', '2024-01-01'),
                async_result.get('config', {}).get('end_date', '2024-12-31')
            ],
            'initial_capital': async_result.get('config', {}).get('initial_capital', 10000)
        }

    except Exception as e:
        st.error(f"结果转换失败: {e}")
        return None

def main():
    """主函数"""
    
    # 页面标题
    st.title("📊 Backtest Analysis")
    st.markdown("---")
    
    # 侧边栏配置
    with st.sidebar:
        st.header("🔧 回测配置")

        # 参数预设管理
        st.subheader("📋 参数预设")

        # 预设选择
        preset_options = ["自定义", "保守型", "平衡型", "激进型", "短线型", "长线型"]
        selected_preset = st.selectbox("选择预设", preset_options, help="选择预定义的参数组合")

        if selected_preset != "自定义":
            if st.button("🔄 加载预设", use_container_width=True):
                load_preset_params(selected_preset)
                st.success(f"已加载 {selected_preset} 预设参数")
                st.rerun()

        # 保存当前配置为预设
        with st.expander("💾 保存预设"):
            preset_name = st.text_input("预设名称", placeholder="输入预设名称")
            if st.button("保存当前配置", disabled=not preset_name):
                save_preset_params(preset_name)
                st.success(f"预设 '{preset_name}' 已保存")

        st.markdown("---")

        # 基础参数
        st.subheader("基础参数")
        initial_capital = st.number_input(
            "初始资金 (¥)",
            min_value=1000,
            max_value=1000000,
            value=BACKTEST_CONFIG["default_capital"],
            step=1000,
            help="回测的初始资金金额"
        )
        
        # 币种选择
        st.subheader("币种选择")
        try:
            available_symbols = get_available_symbols()
        except:
            available_symbols = BACKTEST_CONFIG["default_symbols"] + [
                "BNBUSDT", "SOLUSDT", "XRPUSDT", "DOGEUSDT", "AVAXUSDT",
                "DOTUSDT", "MATICUSDT", "LTCUSDT", "LINKUSDT", "UNIUSDT"
            ]

        # 快速选择按钮
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            if st.button("🔄 全选所有", key="select_all_symbols", help="选择所有可用币种"):
                st.session_state.selected_symbols = available_symbols
                st.success(f"已选择所有 {len(available_symbols)} 个币种！")
        with col2:
            if st.button("❌ 清空选择", key="clear_all_symbols", help="清空所有选择"):
                st.session_state.selected_symbols = []
                st.success("已清空选择！")
        with col3:
            if st.button("🔥 热门币种", key="select_popular", help="选择热门币种"):
                popular_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "XRPUSDT",
                                 "DOGEUSDT", "AVAXUSDT", "DOTUSDT", "MATICUSDT", "LTCUSDT"]
                st.session_state.selected_symbols = [s for s in popular_symbols if s in available_symbols]
                st.success("已选择热门币种！")
        with col4:
            if st.button("📊 默认选择", key="select_default", help="选择默认币种"):
                st.session_state.selected_symbols = available_symbols[:3] if len(available_symbols) >= 3 else available_symbols
                st.success("已选择默认币种！")

        # 初始化session state
        if 'selected_symbols' not in st.session_state:
            st.session_state.selected_symbols = available_symbols[:3] if len(available_symbols) >= 3 else available_symbols

        selected_symbols = st.multiselect(
            "选择交易币种",
            options=available_symbols,
            default=st.session_state.selected_symbols,
            help="选择要进行回测的币种"
        )

        # 更新session state
        st.session_state.selected_symbols = selected_symbols
        
        # 时间范围
        st.subheader("时间范围")
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=365)
        
        date_range = st.date_input(
            "选择回测时间范围",
            value=(start_date, end_date),
            min_value=datetime(2022, 1, 1).date(),
            max_value=end_date,
            help="选择回测的开始和结束日期"
        )
        
        # 策略参数
        st.subheader("策略参数")
        ema_short = st.slider(
            "EMA短期", 5, 50,
            get_preset_value("ema_short", 21),
            help="短期EMA周期"
        )
        ema_medium = st.slider(
            "EMA中期", 20, 100,
            get_preset_value("ema_medium", 55),
            help="中期EMA周期"
        )
        ema_long = st.slider(
            "EMA长期", 100, 300,
            get_preset_value("ema_long", 200),
            help="长期EMA周期"
        )

        # 参数验证提示
        if ema_short >= ema_medium:
            st.error("⚠️ 短期EMA周期必须小于中期EMA周期")
        if ema_medium >= ema_long:
            st.error("⚠️ 中期EMA周期必须小于长期EMA周期")

        # 风险管理
        st.subheader("风险管理")
        stop_loss_pct = st.slider(
            "止损比例", 0.05, 0.30,
            get_preset_value("stop_loss_pct", 0.15),
            0.01, format="%.2f", help="止损百分比"
        )
        take_profit_pct = st.slider(
            "止盈比例", 0.10, 1.00,
            get_preset_value("take_profit_pct", 0.50),
            0.05, format="%.2f", help="分批止盈百分比"
        )
        
        # 高级选项
        with st.expander("🔧 高级选项"):
            enable_partial_profit = st.checkbox("启用分批止盈", value=True)
            signal_cooldown_days = st.slider(
                "信号冷却期(天)", 1, 60,
                get_preset_value("signal_cooldown_days", 30),
                help="两次入场信号之间的最小间隔天数"
            )
            max_positions = st.slider("最大持仓数", 1, 10, 5)

            # 回测模式选择
            st.subheader("回测模式")
            backtest_mode = st.radio(
                "选择回测模式",
                ["快速模式", "标准模式", "详细模式"],
                index=1,
                help="快速模式：简化计算，适合参数调试\n标准模式：完整回测，平衡速度和精度\n详细模式：最详细的分析，速度较慢"
            )
        
        # 回测执行选项
        st.markdown("---")
        col1, col2 = st.columns([2, 1])

        with col1:
            run_backtest = st.button("🚀 开始回测", type="primary", use_container_width=True)

        with col2:
            use_async = st.checkbox("异步执行", value=True, help="长时间回测建议使用异步执行")

    # 主内容区域
    if run_backtest:
        if not selected_symbols:
            st.error("请至少选择一个交易币种")
            return

        if len(date_range) != 2:
            st.error("请选择完整的时间范围")
            return

        # 构建回测配置
        backtest_config = {
            'symbols': selected_symbols,
            'initial_capital': initial_capital,
            'start_date': date_range[0].strftime('%Y-%m-%d'),
            'end_date': date_range[1].strftime('%Y-%m-%d'),
            'strategy_params': {
                'ema_short': ema_short,
                'ema_medium': ema_medium,
                'ema_long': ema_long,
                'stop_loss_pct': stop_loss_pct,
                'take_profit_pct': take_profit_pct,
                'signal_cooldown_days': signal_cooldown_days
            },
            'enable_partial_profit': enable_partial_profit,
            'verbose_level': 1
        }

        if use_async:
            # 异步执行
            try:
                task_id = async_backtest_manager.create_task(backtest_config)
                if async_backtest_manager.start_task(task_id):
                    st.success(f"✅ 回测任务已提交！任务ID: {task_id[:8]}")
                    st.info("💡 请在下方的任务管理区域查看执行进度")
                else:
                    st.error("❌ 启动回测任务失败")

                # 存储任务ID到session state
                if 'backtest_tasks' not in st.session_state:
                    st.session_state.backtest_tasks = []
                st.session_state.backtest_tasks.append(task_id)

            except Exception as e:
                st.error(f"提交异步任务失败: {e}")
        else:
            # 同步执行（快速测试）
            progress_bar = st.progress(0)
            status_text = st.empty()

            with st.spinner("正在运行回测..."):
                try:
                    progress_bar.progress(20)
                    status_text.text("正在执行回测...")

                    # 调用TBTrade集成接口
                    results = tbtrade_integration.run_backtest(backtest_config)

                    progress_bar.progress(100)
                    status_text.text("回测完成！")

                except Exception as e:
                    st.error(f"回测执行失败: {e}")
                    # 如果真实回测失败，使用模拟数据
                    results = {
                        'success': False,
                        'error': str(e),
                        'use_mock': True
                    }

            # 清除进度显示
            progress_bar.empty()
            status_text.empty()

            # 显示回测结果
            if results.get('success', False):
                st.success("✅ 回测完成！")
                # 显示真实回测结果
                display_backtest_results(results['results'])
            else:
                if results.get('use_mock', False):
                    st.warning("⚠️ 使用模拟数据进行回测演示")
                    # 生成模拟数据用于演示
                    mock_results = generate_mock_backtest_results(
                        selected_symbols, date_range, initial_capital
                    )
                    display_backtest_results(mock_results)
                else:
                    st.error(f"❌ 回测失败: {results.get('error', '未知错误')}")

    else:
        # 显示默认内容
        st.info("👈 请在左侧配置回测参数，然后点击'开始回测'按钮")

    # 显示历史回测记录
    st.subheader("📚 历史回测记录")

    # 模拟历史记录
    history_data = [
            {
                "时间": "2024-01-15 10:30",
                "币种": "BTCUSDT, ETHUSDT",
                "初始资金": "¥10,000",
                "最终资金": "¥11,250",
                "收益率": "12.5%",
                "状态": "已完成"
            },
            {
                "时间": "2024-01-10 14:20",
                "币种": "ADAUSDT, DOTUSDT",
                "初始资金": "¥20,000",
                "最终资金": "¥18,900",
                "收益率": "-5.5%",
                "状态": "已完成"
            }
    ]

    history_df = pd.DataFrame(history_data)
    st.dataframe(history_df, use_container_width=True, hide_index=True)

def generate_mock_backtest_results(symbols, date_range, initial_capital):
    """生成模拟回测结果"""
    start_date, end_date = date_range
    days = (end_date - start_date).days
    
    # 生成模拟资产曲线
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    returns = np.random.normal(0.001, 0.02, len(dates))  # 模拟日收益率
    equity_values = [initial_capital]
    
    for ret in returns[1:]:
        equity_values.append(equity_values[-1] * (1 + ret))
    
    equity_df = pd.DataFrame({
        'equity': equity_values,
        'benchmark': [initial_capital * (1 + 0.0005) ** i for i in range(len(dates))]
    }, index=dates)
    
    # 生成模拟交易记录
    num_trades = np.random.randint(5, 20)
    trades_data = []
    
    for i in range(num_trades):
        symbol = np.random.choice(symbols)
        entry_date = start_date + timedelta(days=np.random.randint(0, days-10))
        exit_date = entry_date + timedelta(days=np.random.randint(1, 10))
        
        entry_price = np.random.uniform(20000, 50000)
        exit_price = entry_price * (1 + np.random.normal(0, 0.1))
        quantity = 1000 / entry_price
        pnl = (exit_price - entry_price) * quantity
        
        trades_data.append({
            'symbol': symbol,
            'entry_date': entry_date,
            'exit_date': exit_date,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'quantity': quantity,
            'pnl': pnl,
            'return_pct': (exit_price - entry_price) / entry_price
        })
    
    trades_df = pd.DataFrame(trades_data)
    
    # 生成绩效指标
    total_return = (equity_values[-1] - initial_capital) / initial_capital
    winning_trades = len(trades_df[trades_df['pnl'] > 0])
    win_rate = winning_trades / len(trades_df) if len(trades_df) > 0 else 0
    
    performance_metrics = {
        "总收益率": total_return,
        "年化收益率": total_return * (365 / days),
        "胜率": win_rate,
        "总交易次数": len(trades_df),
        "盈利交易": winning_trades,
        "亏损交易": len(trades_df) - winning_trades,
        "最大回撤": -0.15,  # 模拟值
        "夏普比率": 1.2,    # 模拟值
        "总盈亏": equity_values[-1] - initial_capital,
        "最大单笔盈利": trades_df['pnl'].max() if not trades_df.empty else 0,
        "最大单笔亏损": trades_df['pnl'].min() if not trades_df.empty else 0
    }
    
    return {
        'equity_curve': equity_df,
        'trades': trades_df,
        'performance': performance_metrics,
        'symbols': symbols,
        'date_range': date_range,
        'initial_capital': initial_capital
    }

def display_backtest_results(results):
    """显示回测结果"""
    
    # 关键指标概览
    st.subheader("📈 关键指标")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_return = results['performance']['总收益率']
        st.metric(
            "总收益率",
            f"{total_return:.2%}",
            delta=f"{total_return:.2%}",
            help="整个回测期间的总收益率"
        )
    
    with col2:
        win_rate = results['performance']['胜率']
        st.metric(
            "胜率",
            f"{win_rate:.1%}",
            help="盈利交易占总交易的比例"
        )
    
    with col3:
        total_trades = results['performance']['总交易次数']
        st.metric(
            "总交易次数",
            total_trades,
            help="回测期间的总交易次数"
        )
    
    with col4:
        sharpe_ratio = results['performance']['夏普比率']
        st.metric(
            "夏普比率",
            f"{sharpe_ratio:.2f}",
            help="风险调整后的收益指标"
        )
    
    # 图表展示区域
    chart_tabs = st.tabs(["📈 资产曲线", "📉 回撤分析", "📊 收益分布", "🔥 月度热力图", "🎯 风险雷达"])

    with chart_tabs[0]:
        st.subheader("💰 资产曲线")
        equity_chart = create_equity_curve_chart(results['equity_curve'])
        st.plotly_chart(equity_chart, use_container_width=True)

    with chart_tabs[1]:
        st.subheader("📉 回撤分析")
        drawdown_chart = create_drawdown_chart(results['equity_curve'])
        st.plotly_chart(drawdown_chart, use_container_width=True)

    with chart_tabs[2]:
        st.subheader("📊 收益率分布")
        if not results['trades'].empty:
            returns_chart = create_returns_distribution_chart(results['trades']['return_pct'])
            st.plotly_chart(returns_chart, use_container_width=True)
        else:
            st.info("暂无交易数据")

    with chart_tabs[3]:
        st.subheader("🔥 月度收益热力图")
        if not results['trades'].empty:
            heatmap_chart = create_monthly_returns_heatmap(results['trades'])
            st.plotly_chart(heatmap_chart, use_container_width=True)
        else:
            st.info("暂无交易数据")

    with chart_tabs[4]:
        st.subheader("🎯 风险指标雷达图")
        risk_chart = create_risk_metrics_chart(results['performance'])
        st.plotly_chart(risk_chart, use_container_width=True)
    
    # 交易记录表格
    display_trades_table(results['trades'])
    
    # 详细绩效指标
    display_performance_summary(results['performance'])

def display_task_management():
    """显示任务管理区域"""
    st.markdown("---")
    st.subheader("📋 任务管理")

    # 获取所有任务
    all_tasks = async_backtest_manager.get_all_tasks()

    if not all_tasks:
        st.info("暂无回测任务")
        return

    # 显示任务列表
    for task_info in all_tasks:
        task_id = task_info['task_id']
        status = task_info['status']

        with st.expander(f"任务 {task_id[:8]}... - {status}",
                        expanded=(status == 'running')):

            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.write(f"**状态**: {status}")
                if task_info.get('log_messages'):
                    latest_log = task_info['log_messages'][-1] if task_info['log_messages'] else "无日志"
                    st.write(f"**最新消息**: {latest_log}")

                start_time = task_info.get('start_time')
                if start_time:
                    st.write(f"**开始时间**: {start_time[:19]}")

                # 显示配置信息
                config = task_info['config']
                st.write(f"**币种**: {', '.join(config.get('symbols', []))}")
                st.write(f"**时间范围**: {config.get('start_date')} 至 {config.get('end_date')}")
                st.write(f"**初始资金**: ¥{config.get('initial_capital', 0):,}")

            with col2:
                # 进度条
                progress = task_info['progress']
                st.progress(progress / 100.0)
                st.write(f"进度: {progress:.1f}%")

                # 刷新按钮
                if st.button("🔄 刷新", key=f"refresh_{task_id}"):
                    st.rerun()

            with col3:
                # 操作按钮
                if status in ['pending', 'running']:
                    if st.button("❌ 取消", key=f"cancel_{task_id}"):
                        if async_backtest_manager.cancel_task(task_id):
                            st.success("任务已取消")
                            st.rerun()
                        else:
                            st.error("取消失败")

                elif status == 'completed':
                    if st.button("📊 查看结果", key=f"view_{task_id}"):
                        # 显示结果
                        result = task_info['result']
                        if result:
                            st.success("✅ 回测完成！")
                            # 转换结果格式以适配显示函数
                            display_results = convert_async_results_to_display_format(result)
                            display_backtest_results(display_results)
                        else:
                            st.error("回测结果为空")

                elif status == 'failed':
                    error_msg = task_info.get('error', '未知错误')
                    st.error(f"执行失败: {error_msg}")

    # 清理按钮
    if st.button("🧹 清理已完成任务"):
        async_backtest_manager.clear_completed_tasks()
        st.success("已清理完成的任务")
        st.rerun()

# 任务管理区域（在主逻辑外）
display_task_management()

if __name__ == "__main__":
    main()
