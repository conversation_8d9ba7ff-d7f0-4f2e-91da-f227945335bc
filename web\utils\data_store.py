#!/usr/bin/env python3
"""
全局数据存储管理器
Global Data Store Manager

提供跨页面的数据共享功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

class DataStore:
    """全局数据存储管理器"""
    
    def __init__(self):
        self.data_dir = Path(__file__).parent.parent.parent / "data"
        self.data_dir.mkdir(exist_ok=True)
        self.tasks_file = self.data_dir / "backtest_tasks.json"
    
    def save_tasks(self, tasks_data: Dict[str, Any]) -> bool:
        """保存任务数据"""
        try:
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False, default=str)
            return True
        except Exception as e:
            print(f"保存任务数据失败: {e}")
            return False
    
    def load_tasks(self) -> Dict[str, Any]:
        """加载任务数据"""
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载任务数据失败: {e}")
            return {}
    
    def get_completed_tasks(self) -> List[Dict[str, Any]]:
        """获取已完成的任务"""
        tasks = self.load_tasks()
        completed = []
        
        for task_id, task_data in tasks.items():
            if (task_data.get('status') == 'completed' and 
                task_data.get('result') is not None):
                completed.append({
                    'task_id': task_id,
                    'config': task_data.get('config', {}),
                    'result': task_data.get('result', {}),
                    'start_time': task_data.get('start_time'),
                    'end_time': task_data.get('end_time')
                })
        
        return completed
    
    def add_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """添加或更新任务"""
        try:
            tasks = self.load_tasks()
            tasks[task_id] = task_data
            return self.save_tasks(tasks)
        except Exception as e:
            print(f"添加任务失败: {e}")
            return False
    
    def update_task_status(self, task_id: str, status: str, 
                          result: Optional[Dict[str, Any]] = None) -> bool:
        """更新任务状态"""
        try:
            tasks = self.load_tasks()
            if task_id in tasks:
                tasks[task_id]['status'] = status
                if result:
                    tasks[task_id]['result'] = result
                if status == 'completed':
                    tasks[task_id]['end_time'] = datetime.now().isoformat()
                return self.save_tasks(tasks)
            return False
        except Exception as e:
            print(f"更新任务状态失败: {e}")
            return False

# 全局实例
data_store = DataStore()
